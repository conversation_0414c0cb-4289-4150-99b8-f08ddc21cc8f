{"name": "jsp2react-agent", "version": "1.0.0", "description": "AI Agent CLI tool for migrating JSP applications to React", "main": "dist/index.js", "bin": {"jsp2react": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "ts-node src/cli.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "start": "node dist/cli.js"}, "keywords": ["jsp", "react", "migration", "ai", "agent", "cli"], "author": "phodal", "license": "MIT", "dependencies": {"commander": "^11.1.0", "puppeteer": "^21.5.2", "cheerio": "^1.0.0-rc.12", "fs-extra": "^11.1.1", "chalk": "^4.1.2", "ora": "^5.4.1"}, "devDependencies": {"@types/node": "^20.8.0", "@types/fs-extra": "^11.0.2", "@types/cheerio": "^0.22.31", "@types/jest": "^29.5.5", "typescript": "^5.2.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.50.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4"}}