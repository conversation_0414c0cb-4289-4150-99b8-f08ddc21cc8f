import { JspAnalysisService } from '../services/JspAnalysisService';
import * as fs from 'fs-extra';
import * as path from 'path';

// Mock fs-extra
jest.mock('fs-extra');
const mockedFs = fs as jest.Mocked<typeof fs>;

describe('JspAnalysisService', () => {
  let service: JspAnalysisService;
  const mockSourcePath = '/mock/source';

  beforeEach(() => {
    service = new JspAnalysisService(mockSourcePath);
    jest.clearAllMocks();
  });

  describe('analyzeProject', () => {
    it('should analyze a JSP project and return project structure', async () => {
      // Mock file system
      mockedFs.pathExists.mockResolvedValue(true);
      mockedFs.readdir.mockResolvedValue(['posts.jsp', 'create.jsp'] as any);
      mockedFs.stat.mockResolvedValue({ isDirectory: () => false } as any);
      
      const mockJspContent = `
        <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
        <html>
        <head>
          <title>Test Page</title>
          <link rel="stylesheet" href="/css/styles.css">
        </head>
        <body>
          <c:forEach items="\${posts}" var="post">
            <h3>\${post.title}</h3>
          </c:forEach>
          <form action="/posts" method="post">
            <input type="text" name="title" required/>
            <input type="submit" value="Submit"/>
          </form>
        </body>
        </html>
      `;
      
      mockedFs.readFile.mockResolvedValue(mockJspContent);

      const result = await service.analyzeProject();

      expect(result).toHaveProperty('jspPages');
      expect(result).toHaveProperty('routeMappings');
      expect(result).toHaveProperty('sharedComponents');
      expect(result).toHaveProperty('cssFiles');
      expect(result.jspPages).toHaveLength(2);
    });
  });

  describe('JSP content analysis', () => {
    it('should extract title from JSP content', () => {
      const content = '<html><head><title>Test Title</title></head></html>';
      const service = new JspAnalysisService('/test');
      
      // Access private method for testing
      const extractTitle = (service as any).extractTitle.bind(service);
      const title = extractTitle(content);
      
      expect(title).toBe('Test Title');
    });

    it('should extract JSTL tags from JSP content', () => {
      const content = `
        <c:forEach items="\${posts}" var="post">
          <h3>\${post.title}</h3>
        </c:forEach>
        <c:if test="\${user.isAdmin}">
          <p>Admin content</p>
        </c:if>
      `;
      
      const service = new JspAnalysisService('/test');
      const extractJstlTags = (service as any).extractJstlTags.bind(service);
      const tags = extractJstlTags(content);
      
      expect(tags).toHaveLength(3); // forEach, if, and EL expressions
      expect(tags[0].type).toBe('forEach');
      expect(tags[1].type).toBe('if');
    });

    it('should extract forms from JSP content', () => {
      const content = `
        <form action="/posts" method="post">
          <input type="text" name="title" required/>
          <textarea name="content" required></textarea>
          <input type="submit" value="Submit"/>
        </form>
      `;
      
      const service = new JspAnalysisService('/test');
      const extractForms = (service as any).extractForms.bind(service);
      const forms = extractForms(content);
      
      expect(forms).toHaveLength(1);
      expect(forms[0].action).toBe('/posts');
      expect(forms[0].method).toBe('post');
      expect(forms[0].inputs).toHaveLength(2); // text input and textarea (submit excluded)
    });

    it('should extract links from JSP content', () => {
      const content = `
        <a href="/posts">All Posts</a>
        <a href="/create">Create New</a>
        <a href="https://external.com">External Link</a>
      `;
      
      const service = new JspAnalysisService('/test');
      const extractLinks = (service as any).extractLinks.bind(service);
      const links = extractLinks(content);
      
      expect(links).toHaveLength(3);
      expect(links[0].href).toBe('/posts');
      expect(links[0].text).toBe('All Posts');
    });
  });

  describe('route mapping generation', () => {
    it('should generate correct route mappings', () => {
      const mockPages = [
        { name: 'posts', path: 'posts.jsp', content: '', title: 'Posts', dependencies: [], jstlTags: [], forms: [], links: [] },
        { name: 'create', path: 'create.jsp', content: '', title: 'Create', dependencies: [], jstlTags: [], forms: [], links: [] },
        { name: 'edit', path: 'edit.jsp', content: '', title: 'Edit', dependencies: [], jstlTags: [], forms: [], links: [] }
      ];
      
      const service = new JspAnalysisService('/test');
      const generateRouteMappings = (service as any).generateRouteMappings.bind(service);
      const mappings = generateRouteMappings(mockPages);
      
      expect(mappings).toHaveLength(3);
      expect(mappings[0].jspPath).toBe('/posts.jsp');
      expect(mappings[0].reactPath).toBe('/');
      expect(mappings[0].component).toBe('PostsPage');
      
      expect(mappings[1].reactPath).toBe('/create');
      expect(mappings[2].reactPath).toBe('/edit/[id]');
    });
  });
});
