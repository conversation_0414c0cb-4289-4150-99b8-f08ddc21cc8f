import { JspToReactConverter } from '../converters/JspToReactConverter';
import { JspPage } from '../types';

describe('JspToReactConverter', () => {
  let converter: JspToReactConverter;

  beforeEach(() => {
    converter = new JspToReactConverter();
  });

  describe('convertJspToReact', () => {
    it('should convert a simple JSP page to React component', () => {
      const mockJspPage: JspPage = {
        name: 'posts',
        path: 'posts.jsp',
        content: `
          <html>
          <head>
            <title>Posts</title>
          </head>
          <body>
            <h1>All Posts</h1>
            <div class="container">
              <p>Welcome to the blog</p>
            </div>
          </body>
          </html>
        `,
        title: 'Posts',
        dependencies: [],
        jstlTags: [],
        forms: [],
        links: []
      };

      const result = converter.convertJspToReact(mockJspPage);

      expect(result).toContain('export default function PostsPage()');
      expect(result).toContain('className="container"');
      expect(result).toContain('<h1>All Posts</h1>');
    });

    it('should convert JSP with JSTL forEach to React map', () => {
      const mockJspPage: JspPage = {
        name: 'posts',
        path: 'posts.jsp',
        content: `
          <html>
          <body>
            <c:forEach items="\${posts}" var="post">
              <h3>\${post.title}</h3>
              <p>\${post.content}</p>
            </c:forEach>
          </body>
          </html>
        `,
        title: 'Posts',
        dependencies: [],
        jstlTags: [
          {
            type: 'forEach',
            attributes: { items: '${posts}', var: 'post' },
            content: '<c:forEach items="${posts}" var="post">',
            lineNumber: 4
          }
        ],
        forms: [],
        links: []
      };

      const result = converter.convertJspToReact(mockJspPage);

      expect(result).toContain('useState([])');
      expect(result).toContain('posts.map((post, index) =>');
      expect(result).toContain('{post.title}');
      expect(result).toContain('{post.content}');
    });

    it('should convert forms with proper React state management', () => {
      const mockJspPage: JspPage = {
        name: 'create',
        path: 'create.jsp',
        content: `
          <html>
          <body>
            <form action="/posts" method="post">
              <input type="text" name="title" required/>
              <textarea name="content" required></textarea>
              <input type="submit" value="Submit"/>
            </form>
          </body>
          </html>
        `,
        title: 'Create',
        dependencies: [],
        jstlTags: [],
        forms: [
          {
            action: '/posts',
            method: 'post',
            inputs: [
              { type: 'text', name: 'title', required: true },
              { type: 'textarea', name: 'content', required: true }
            ]
          }
        ],
        links: []
      };

      const result = converter.convertJspToReact(mockJspPage);

      expect(result).toContain('useState({ title: \'\', content: \'\' })');
      expect(result).toContain('onSubmit={handleSubmit}');
      expect(result).toContain('value={formData.title}');
      expect(result).toContain('onChange={(e) => setFormData({...formData, title: e.target.value})}');
    });

    it('should convert links to Next.js Link components', () => {
      const mockJspPage: JspPage = {
        name: 'posts',
        path: 'posts.jsp',
        content: `
          <html>
          <body>
            <a href="/create">Create New Post</a>
            <a href="/posts/1">View Post</a>
          </body>
          </html>
        `,
        title: 'Posts',
        dependencies: [],
        jstlTags: [],
        forms: [],
        links: [
          { href: '/create', text: 'Create New Post' },
          { href: '/posts/1', text: 'View Post' }
        ]
      };

      const result = converter.convertJspToReact(mockJspPage);

      expect(result).toContain('import Link from \'next/link\';');
      expect(result).toContain('<Link href="/create">Create New Post</Link>');
      expect(result).toContain('<Link href="/posts/1">View Post</Link>');
    });
  });

  describe('EL expression conversion', () => {
    it('should convert simple EL expressions', () => {
      const converter = new JspToReactConverter();
      const convertElExpressions = (converter as any).convertElExpressions.bind(converter);
      
      const input = 'Hello ${user.name}, you have ${user.messageCount} messages';
      const result = convertElExpressions(input);
      
      expect(result).toBe('Hello {user.name}, you have {user.messageCount} messages');
    });

    it('should convert formatter expressions', () => {
      const converter = new JspToReactConverter();
      const convertElExpression = (converter as any).convertElExpression.bind(converter);
      
      const input = 'formatter.format(post.date)';
      const result = convertElExpression(input);
      
      expect(result).toBe('new Date(post.date).toLocaleDateString()');
    });
  });

  describe('generateRouterConfig', () => {
    it('should generate router configuration', () => {
      const routeMappings = [
        { jspPath: '/posts.jsp', reactPath: '/', component: 'PostsPage' },
        { jspPath: '/create.jsp', reactPath: '/create', component: 'CreatePage' },
        { jspPath: '/edit.jsp', reactPath: '/edit/[id]', component: 'EditPage' }
      ];

      const result = converter.generateRouterConfig(routeMappings);

      expect(result).toContain('export const routes = [');
      expect(result).toContain('path: \'/\'');
      expect(result).toContain('component: PostsPage');
      expect(result).toContain('path: \'/create\'');
      expect(result).toContain('path: \'/edit/[id]\'');
    });
  });
});
