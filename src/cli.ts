#!/usr/bin/env node

import { Command } from 'commander';
import * as path from 'path';
import * as fs from 'fs-extra';
import chalk from 'chalk';
import ora from 'ora';
import { JspAnalysisService } from './services/JspAnalysisService';
import { JspToReactConverter } from './converters/JspToReactConverter';
import { MigrationConfig } from './types';

const program = new Command();

program
  .name('jsp2react')
  .description('AI Agent CLI tool for migrating JSP applications to React')
  .version('1.0.0');

program
  .command('analyze')
  .description('Analyze JSP project structure')
  .option('-s, --source <path>', 'Source JSP project path', './fixtures/source')
  .action(async (options) => {
    const spinner = ora('Analyzing JSP project...').start();
    
    try {
      const sourcePath = path.resolve(options.source);
      
      if (!await fs.pathExists(sourcePath)) {
        spinner.fail(`Source path does not exist: ${sourcePath}`);
        process.exit(1);
      }

      const analysisService = new JspAnalysisService(sourcePath);
      const projectStructure = await analysisService.analyzeProject();
      
      spinner.succeed('Analysis completed!');
      
      console.log(chalk.blue('\n📊 Project Analysis Results:\n'));
      
      console.log(chalk.green('JSP Pages found:'));
      projectStructure.jspPages.forEach(page => {
        console.log(`  • ${page.name}.jsp - ${page.title || 'No title'}`);
        console.log(`    Forms: ${page.forms.length}, Links: ${page.links.length}, JSTL Tags: ${page.jstlTags.length}`);
      });
      
      console.log(chalk.green('\nRoute Mappings:'));
      projectStructure.routeMappings.forEach(mapping => {
        console.log(`  ${mapping.jspPath} → ${mapping.reactPath} (${mapping.component})`);
      });
      
      console.log(chalk.green('\nShared Components:'));
      projectStructure.sharedComponents.forEach(component => {
        console.log(`  • ${component}`);
      });
      
      console.log(chalk.green('\nCSS Files:'));
      projectStructure.cssFiles.forEach(file => {
        console.log(`  • ${file}`);
      });
      
    } catch (error) {
      spinner.fail('Analysis failed');
      console.error(chalk.red('Error:'), error);
      process.exit(1);
    }
  });

program
  .command('convert')
  .description('Convert JSP pages to React components')
  .option('-s, --source <path>', 'Source JSP project path', './fixtures/source')
  .option('-t, --target <path>', 'Target React project path', './fixtures/target')
  .option('-p, --page <name>', 'Convert specific page (optional)')
  .action(async (options) => {
    const spinner = ora('Converting JSP to React...').start();
    
    try {
      const sourcePath = path.resolve(options.source);
      const targetPath = path.resolve(options.target);
      
      if (!await fs.pathExists(sourcePath)) {
        spinner.fail(`Source path does not exist: ${sourcePath}`);
        process.exit(1);
      }
      
      if (!await fs.pathExists(targetPath)) {
        spinner.fail(`Target path does not exist: ${targetPath}`);
        process.exit(1);
      }

      const analysisService = new JspAnalysisService(sourcePath);
      const converter = new JspToReactConverter();
      
      spinner.text = 'Analyzing JSP project...';
      const projectStructure = await analysisService.analyzeProject();
      
      const pagesToConvert = options.page 
        ? projectStructure.jspPages.filter(page => page.name === options.page)
        : projectStructure.jspPages;
      
      if (pagesToConvert.length === 0) {
        spinner.fail(`No pages found to convert${options.page ? ` (${options.page})` : ''}`);
        process.exit(1);
      }
      
      spinner.text = 'Converting pages...';
      
      for (const page of pagesToConvert) {
        const reactComponent = converter.convertJspToReact(page);
        const componentPath = path.join(targetPath, 'src/app', `${page.name}`, 'page.tsx');
        
        await fs.ensureDir(path.dirname(componentPath));
        await fs.writeFile(componentPath, reactComponent);
        
        console.log(chalk.green(`✓ Converted ${page.name}.jsp → ${path.relative(targetPath, componentPath)}`));
      }
      
      // Generate router configuration
      const routerConfig = converter.generateRouterConfig(projectStructure.routeMappings);
      const routerPath = path.join(targetPath, 'src/config/routes.ts');
      await fs.ensureDir(path.dirname(routerPath));
      await fs.writeFile(routerPath, routerConfig);
      
      spinner.succeed(`Conversion completed! Converted ${pagesToConvert.length} page(s)`);
      
      console.log(chalk.blue('\n📝 Next steps:'));
      console.log('1. Review the generated React components');
      console.log('2. Update API calls and data fetching logic');
      console.log('3. Test the converted pages');
      console.log('4. Run: npm run dev in the target directory');
      
    } catch (error) {
      spinner.fail('Conversion failed');
      console.error(chalk.red('Error:'), error);
      process.exit(1);
    }
  });

program
  .command('migrate')
  .description('Full migration from JSP to React (analyze + convert)')
  .option('-s, --source <path>', 'Source JSP project path', './fixtures/source')
  .option('-t, --target <path>', 'Target React project path', './fixtures/target')
  .action(async (options) => {
    console.log(chalk.blue('🚀 Starting full JSP to React migration...\n'));
    
    // Run analyze first
    await program.parseAsync(['node', 'cli.js', 'analyze', '-s', options.source]);
    
    console.log('\n');
    
    // Then run convert
    await program.parseAsync(['node', 'cli.js', 'convert', '-s', options.source, '-t', options.target]);
  });

program
  .command('init')
  .description('Initialize a new React project for migration')
  .option('-t, --target <path>', 'Target directory for React project', './target')
  .action(async (options) => {
    const spinner = ora('Initializing React project...').start();
    
    try {
      const targetPath = path.resolve(options.target);
      
      if (await fs.pathExists(targetPath)) {
        spinner.fail(`Target directory already exists: ${targetPath}`);
        process.exit(1);
      }
      
      // This would typically run: npx create-next-app@latest
      spinner.text = 'Creating Next.js application...';
      
      // For now, just create a basic structure
      await fs.ensureDir(targetPath);
      
      spinner.succeed('React project initialized!');
      console.log(chalk.green(`✓ Created React project at: ${targetPath}`));
      console.log(chalk.blue('\nNext steps:'));
      console.log(`1. cd ${path.relative(process.cwd(), targetPath)}`);
      console.log('2. npm install');
      console.log('3. Run jsp2react migrate to start migration');
      
    } catch (error) {
      spinner.fail('Initialization failed');
      console.error(chalk.red('Error:'), error);
      process.exit(1);
    }
  });

// Error handling
program.on('command:*', () => {
  console.error(chalk.red('Invalid command: %s\nSee --help for a list of available commands.'), program.args.join(' '));
  process.exit(1);
});

if (process.argv.length === 2) {
  program.help();
}

program.parse(process.argv);
