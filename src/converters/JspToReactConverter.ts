import * as cheerio from 'cheerio';
import { JspPage, JstlTag, FormInfo } from '../types';

export class JspToReactConverter {
  
  convertJspToReact(jspPage: JspPage): string {
    const componentName = this.jspNameToComponentName(jspPage.name);
    const jsxContent = this.convertHtmlToJsx(jspPage.content);
    const imports = this.generateImports(jspPage);
    const hooks = this.generateHooks(jspPage);
    
    return `${imports}

export default function ${componentName}() {
${hooks}
  return (
${jsxContent}
  );
}`;
  }

  private jspNameToComponentName(jspName: string): string {
    return jspName.charAt(0).toUpperCase() + jspName.slice(1) + 'Page';
  }

  private generateImports(jspPage: JspPage): string {
    const imports = ["import React from 'react';"];
    
    // Add useState if there are forms
    if (jspPage.forms.length > 0) {
      imports[0] = "import React, { useState } from 'react';";
    }

    // Add Next.js imports if needed
    if (jspPage.links.some(link => link.href.startsWith('/'))) {
      imports.push("import Link from 'next/link';");
    }

    return imports.join('\n');
  }

  private generateHooks(jspPage: JspPage): string {
    const hooks: string[] = [];

    // Generate state for forms
    jspPage.forms.forEach((form, index) => {
      const stateVars = form.inputs
        .filter(input => input.type !== 'submit')
        .map(input => `${input.name}: ''`)
        .join(', ');
      
      if (stateVars) {
        hooks.push(`  const [formData${index || ''}] = useState({ ${stateVars} });`);
      }
    });

    // Generate state for dynamic data (from JSTL)
    const hasForEach = jspPage.jstlTags.some(tag => tag.type === 'forEach');
    if (hasForEach) {
      hooks.push(`  const [posts, setPosts] = useState([]);`);
      hooks.push(`  const [loading, setLoading] = useState(true);`);
    }

    return hooks.length > 0 ? hooks.join('\n') + '\n' : '';
  }

  private convertHtmlToJsx(content: string): string {
    // Remove JSP directives and taglib declarations
    let cleanContent = content
      .replace(/<%@.*?%>/gs, '')
      .replace(/<%.*?%>/gs, '');

    // Load with cheerio for DOM manipulation
    const $ = cheerio.load(cleanContent, { 
      xmlMode: false,
      decodeEntities: false 
    });

    // Convert JSTL tags
    this.convertJstlTags($);
    
    // Convert forms
    this.convertForms($);
    
    // Convert links
    this.convertLinks($);
    
    // Convert attributes to JSX format
    this.convertAttributesToJsx($);

    // Get the body content or full HTML if no body
    const bodyContent = $('body').html() || $.html();
    
    // Clean up and format for JSX
    return this.formatForJsx(bodyContent || '');
  }

  private convertJstlTags($: cheerio.CheerioAPI): void {
    // Convert c:forEach
    $('c\\:forEach, forEach').each((_, element) => {
      const $el = $(element);
      const items = $el.attr('items') || '';
      const varName = $el.attr('var') || 'item';
      
      // Extract the variable name from ${items}
      const itemsVar = items.replace(/^\$\{|\}$/g, '');
      
      const content = $el.html() || '';
      const convertedContent = this.convertElExpressions(content, varName);
      
      $el.replaceWith(`{${itemsVar}.map((${varName}, index) => (
        <div key={index}>
          ${convertedContent}
        </div>
      ))}`);
    });

    // Convert c:if
    $('c\\:if, if').each((_, element) => {
      const $el = $(element);
      const test = $el.attr('test') || '';
      const condition = this.convertElExpression(test);
      const content = $el.html() || '';
      
      $el.replaceWith(`{${condition} && (
        ${content}
      )}`);
    });
  }

  private convertElExpressions(content: string, varName?: string): string {
    return content.replace(/\$\{([^}]+)\}/g, (match, expression) => {
      return `{${this.convertElExpression(expression, varName)}}`;
    });
  }

  private convertElExpression(expression: string, varName?: string): string {
    // Handle simple variable references
    if (varName && expression.startsWith(`${varName}.`)) {
      return expression;
    }
    
    // Handle formatter calls
    if (expression.includes('formatter.format(')) {
      return expression.replace('formatter.format(', 'new Date(').replace(/\)$/, ').toLocaleDateString()');
    }
    
    // Handle other expressions
    return expression;
  }

  private convertForms($: cheerio.CheerioAPI): void {
    $('form').each((_, element) => {
      const $form = $(element);
      const action = $form.attr('action') || '';
      const method = $form.attr('method') || 'get';
      
      // Add onSubmit handler
      $form.attr('onSubmit', 'handleSubmit');
      
      // Convert input elements
      $form.find('input').each((_, input) => {
        const $input = $(input);
        const type = $input.attr('type') || 'text';
        const name = $input.attr('name') || '';
        
        if (type !== 'submit' && name) {
          $input.attr('value', `{formData.${name}}`);
          $input.attr('onChange', `{(e) => setFormData({...formData, ${name}: e.target.value})}`);
        }
      });

      // Convert textarea elements
      $form.find('textarea').each((_, textarea) => {
        const $textarea = $(textarea);
        const name = $textarea.attr('name') || '';
        
        if (name) {
          $textarea.attr('value', `{formData.${name}}`);
          $textarea.attr('onChange', `{(e) => setFormData({...formData, ${name}: e.target.value})}`);
        }
      });
    });
  }

  private convertLinks($: cheerio.CheerioAPI): void {
    $('a').each((_, element) => {
      const $link = $(element);
      const href = $link.attr('href') || '';
      
      if (href.startsWith('/') || href.startsWith('./')) {
        const linkText = $link.text();
        $link.replaceWith(`<Link href="${href}">${linkText}</Link>`);
      }
    });
  }

  private convertAttributesToJsx($: cheerio.CheerioAPI): void {
    $('*').each((_, element) => {
      const $el = $(element);
      
      // Convert class to className
      const classAttr = $el.attr('class');
      if (classAttr) {
        $el.removeAttr('class');
        $el.attr('className', classAttr);
      }
      
      // Convert for to htmlFor (for labels)
      const forAttr = $el.attr('for');
      if (forAttr) {
        $el.removeAttr('for');
        $el.attr('htmlFor', forAttr);
      }
    });
  }

  private formatForJsx(content: string): string {
    // Add proper indentation
    const lines = content.split('\n');
    const indentedLines = lines.map(line => {
      if (line.trim()) {
        return '    ' + line;
      }
      return line;
    });
    
    return indentedLines.join('\n');
  }

  generateRouterConfig(routeMappings: any[]): string {
    const routes = routeMappings.map(mapping => {
      return `  {
    path: '${mapping.reactPath}',
    component: ${mapping.component}
  }`;
    }).join(',\n');

    return `// Auto-generated router configuration
export const routes = [
${routes}
];`;
  }
}
