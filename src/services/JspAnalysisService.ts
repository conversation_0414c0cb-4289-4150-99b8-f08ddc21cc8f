import * as fs from 'fs-extra';
import * as path from 'path';
import { JspPage, JstlTag, FormInfo, InputInfo, LinkInfo, ProjectStructure, RouteMapping } from '../types';

export class JspAnalysisService {
  private sourcePath: string;

  constructor(sourcePath: string) {
    this.sourcePath = sourcePath;
  }

  async analyzeProject(): Promise<ProjectStructure> {
    const jspPages = await this.findAndAnalyzeJspPages();
    const routeMappings = this.generateRouteMappings(jspPages);
    const sharedComponents = this.identifySharedComponents(jspPages);
    const cssFiles = await this.findCssFiles();

    return {
      jspPages,
      routeMappings,
      sharedComponents,
      cssFiles
    };
  }

  private async findAndAnalyzeJspPages(): Promise<JspPage[]> {
    const jspFiles = await this.findJspFiles();
    const pages: JspPage[] = [];

    for (const filePath of jspFiles) {
      const content = await fs.readFile(filePath, 'utf-8');
      const page = this.analyzeJspPage(filePath, content);
      pages.push(page);
    }

    return pages;
  }

  private async findJspFiles(): Promise<string[]> {
    const jspFiles: string[] = [];
    
    const searchDir = path.join(this.sourcePath, 'src/main/webapp');
    if (!await fs.pathExists(searchDir)) {
      return jspFiles;
    }

    const walk = async (dir: string) => {
      const files = await fs.readdir(dir);
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = await fs.stat(filePath);
        
        if (stat.isDirectory()) {
          await walk(filePath);
        } else if (file.endsWith('.jsp')) {
          jspFiles.push(filePath);
        }
      }
    };

    await walk(searchDir);
    return jspFiles;
  }

  private analyzeJspPage(filePath: string, content: string): JspPage {
    const name = path.basename(filePath, '.jsp');
    const relativePath = path.relative(this.sourcePath, filePath);
    
    return {
      path: relativePath,
      name,
      content,
      title: this.extractTitle(content),
      dependencies: this.extractDependencies(content),
      jstlTags: this.extractJstlTags(content),
      forms: this.extractForms(content),
      links: this.extractLinks(content)
    };
  }

  private extractTitle(content: string): string | undefined {
    const titleMatch = content.match(/<title>(.*?)<\/title>/i);
    return titleMatch ? titleMatch[1].trim() : undefined;
  }

  private extractDependencies(content: string): string[] {
    const dependencies: string[] = [];
    
    // Extract taglib dependencies
    const taglibMatches = content.matchAll(/<%@\s*taglib\s+uri="([^"]+)"\s+prefix="([^"]+)"\s*%>/g);
    for (const match of taglibMatches) {
      dependencies.push(`taglib:${match[2]}:${match[1]}`);
    }

    // Extract CSS dependencies
    const cssMatches = content.matchAll(/<link[^>]+href="([^"]+\.css)"[^>]*>/g);
    for (const match of cssMatches) {
      dependencies.push(`css:${match[1]}`);
    }

    return dependencies;
  }

  private extractJstlTags(content: string): JstlTag[] {
    const tags: JstlTag[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      // Extract c:forEach tags
      const forEachMatch = line.match(/<c:forEach\s+([^>]+)>/);
      if (forEachMatch) {
        const attributes = this.parseAttributes(forEachMatch[1]);
        tags.push({
          type: 'forEach',
          attributes,
          content: line.trim(),
          lineNumber: index + 1
        });
      }

      // Extract c:if tags
      const ifMatch = line.match(/<c:if\s+([^>]+)>/);
      if (ifMatch) {
        const attributes = this.parseAttributes(ifMatch[1]);
        tags.push({
          type: 'if',
          attributes,
          content: line.trim(),
          lineNumber: index + 1
        });
      }

      // Extract EL expressions
      const elMatches = line.matchAll(/\$\{([^}]+)\}/g);
      for (const match of elMatches) {
        tags.push({
          type: 'out',
          attributes: { value: match[1] },
          content: match[0],
          lineNumber: index + 1
        });
      }
    });

    return tags;
  }

  private parseAttributes(attributeString: string): Record<string, string> {
    const attributes: Record<string, string> = {};
    const matches = attributeString.matchAll(/(\w+)="([^"]+)"/g);
    
    for (const match of matches) {
      attributes[match[1]] = match[2];
    }

    return attributes;
  }

  private extractForms(content: string): FormInfo[] {
    const forms: FormInfo[] = [];
    const formMatches = content.matchAll(/<form[^>]*action="([^"]*)"[^>]*method="([^"]*)"[^>]*>(.*?)<\/form>/gs);

    for (const match of formMatches) {
      const action = match[1];
      const method = match[2];
      const formContent = match[3];
      const inputs = this.extractInputs(formContent);

      forms.push({
        action,
        method,
        inputs
      });
    }

    return forms;
  }

  private extractInputs(formContent: string): InputInfo[] {
    const inputs: InputInfo[] = [];
    const inputMatches = formContent.matchAll(/<input[^>]*>/g);

    for (const match of inputMatches) {
      const inputTag = match[0];
      const type = this.extractAttribute(inputTag, 'type') || 'text';
      const name = this.extractAttribute(inputTag, 'name') || '';
      const required = inputTag.includes('required');
      const className = this.extractAttribute(inputTag, 'class');

      if (name) {
        inputs.push({
          type,
          name,
          required,
          className
        });
      }
    }

    // Also extract textarea elements
    const textareaMatches = formContent.matchAll(/<textarea[^>]*name="([^"]*)"[^>]*>/g);
    for (const match of textareaMatches) {
      inputs.push({
        type: 'textarea',
        name: match[1],
        required: match[0].includes('required')
      });
    }

    return inputs;
  }

  private extractAttribute(tag: string, attributeName: string): string | undefined {
    const match = tag.match(new RegExp(`${attributeName}="([^"]*)"`, 'i'));
    return match ? match[1] : undefined;
  }

  private extractLinks(content: string): LinkInfo[] {
    const links: LinkInfo[] = [];
    const linkMatches = content.matchAll(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gs);

    for (const match of linkMatches) {
      links.push({
        href: match[1],
        text: match[2].replace(/<[^>]*>/g, '').trim()
      });
    }

    return links;
  }

  private generateRouteMappings(jspPages: JspPage[]): RouteMapping[] {
    return jspPages.map(page => {
      const reactPath = this.jspPathToReactPath(page.name);
      const component = this.jspNameToComponentName(page.name);

      return {
        jspPath: `/${page.name}.jsp`,
        reactPath,
        component
      };
    });
  }

  private jspPathToReactPath(jspName: string): string {
    if (jspName === 'posts') return '/';
    if (jspName === 'create') return '/create';
    if (jspName === 'edit') return '/edit/[id]';
    if (jspName === 'post') return '/post/[id]';
    return `/${jspName}`;
  }

  private jspNameToComponentName(jspName: string): string {
    return jspName.charAt(0).toUpperCase() + jspName.slice(1) + 'Page';
  }

  private identifySharedComponents(jspPages: JspPage[]): string[] {
    // Simple heuristic: identify common patterns
    const sharedComponents: string[] = [];
    
    // Check if multiple pages have forms
    const pagesWithForms = jspPages.filter(page => page.forms.length > 0);
    if (pagesWithForms.length > 1) {
      sharedComponents.push('FormComponent');
    }

    // Check if multiple pages have similar link patterns
    const pagesWithLinks = jspPages.filter(page => page.links.length > 0);
    if (pagesWithLinks.length > 1) {
      sharedComponents.push('NavigationComponent');
    }

    return sharedComponents;
  }

  private async findCssFiles(): Promise<string[]> {
    const cssFiles: string[] = [];
    const cssDir = path.join(this.sourcePath, 'src/main/webapp/css');
    
    if (await fs.pathExists(cssDir)) {
      const files = await fs.readdir(cssDir);
      cssFiles.push(...files.filter(file => file.endsWith('.css')));
    }

    return cssFiles;
  }
}
