export interface JspPage {
  path: string;
  name: string;
  content: string;
  title?: string;
  dependencies: string[];
  jstlTags: JstlTag[];
  forms: FormInfo[];
  links: LinkInfo[];
}

export interface JstlTag {
  type: 'forEach' | 'if' | 'choose' | 'when' | 'otherwise' | 'set' | 'out';
  attributes: Record<string, string>;
  content: string;
  lineNumber: number;
}

export interface FormInfo {
  action: string;
  method: string;
  inputs: InputInfo[];
}

export interface InputInfo {
  type: string;
  name: string;
  required: boolean;
  className?: string;
}

export interface LinkInfo {
  href: string;
  text: string;
}

export interface RouteMapping {
  jspPath: string;
  reactPath: string;
  component: string;
}

export interface ProjectStructure {
  jspPages: JspPage[];
  routeMappings: RouteMapping[];
  sharedComponents: string[];
  cssFiles: string[];
}

export interface MigrationConfig {
  sourcePath: string;
  targetPath: string;
  baseUrl?: string;
  includePatterns?: string[];
  excludePatterns?: string[];
}
